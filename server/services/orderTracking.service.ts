import { prisma } from "../db";
import { errorResponse, successResponse } from "../utils/response.util";

interface OrderStatusUpdate {
  orderId: string;
  status: string;
  description?: string;
  createdBy?: string;
}

class OrderTrackingService {
  /**
   * Generate unique invoice number
   */
  private generateInvoiceNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `INV-${timestamp.slice(-8)}-${random}`;
  }

  /**
   * Generate tracking number
   */
  private generateTrackingNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substr(2, 6).toUpperCase();
    return `TRK-${timestamp.slice(-6)}-${random}`;
  }

  /**
   * Create order with initial status
   */
  async createOrderWithTracking(orderData: any) {
    try {
      const invoiceNumber = this.generateInvoiceNumber();

      const order = await prisma.$transaction(async (tx) => {
        // Create order
        const newOrder = await tx.order.create({
          data: {
            ...orderData,
            orderNumber: invoiceNumber,
            status: 'pending_payment'
          },
          include: {
            items: {
              include: {
                product: true
              }
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            },
            shippingAddress: true
          }
        });

        // Create initial status history
        await tx.orderStatusHistory.create({
          data: {
            orderId: newOrder.id,
            status: 'pending_payment',
            description: 'Order created, waiting for payment',
            createdBy: orderData.userId
          }
        });

        return newOrder;
      });

      return successResponse("Order created successfully", {
        order,
        invoiceNumber,
        trackingUrl: `/order-tracking/${order.id}`
      });

    } catch (error) {
      console.error('Create order with tracking error:', error);
      return errorResponse("Failed to create order");
    }
  }

  /**
   * Update order status with history tracking
   */
  async updateOrderStatus(data: OrderStatusUpdate) {
    try {
      const { orderId, status, description, createdBy } = data;

      const result = await prisma.$transaction(async (tx) => {
        // Update order status
        const updatedOrder = await tx.order.update({
          where: { id: orderId },
          data: { 
            status,
            updatedAt: new Date(),
            // Generate tracking number when status becomes 'shipped'
            ...(status === 'shipped' && { trackingNumber: this.generateTrackingNumber() })
          },
          include: {
            items: {
              include: {
                product: true
              }
            },
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            },
            shippingAddress: true,
            statusHistory: {
              orderBy: { createdAt: 'desc' }
            }
          }
        });

        // Add status history entry
        await tx.orderStatusHistory.create({
          data: {
            orderId,
            status,
            description: description || this.getDefaultStatusDescription(status),
            createdBy
          }
        });

        return updatedOrder;
      });

      return successResponse("Order status updated successfully", result);

    } catch (error) {
      console.error('Update order status error:', error);
      return errorResponse("Failed to update order status");
    }
  }

  /**
   * Get order tracking information
   */
  async getOrderTracking(orderId: string, userId?: string) {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: {
                    take: 1,
                    orderBy: { sortOrder: 'asc' }
                  }
                }
              }
            }
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          shippingAddress: true,
          statusHistory: {
            orderBy: { createdAt: 'asc' }
          },
          payment: true
        }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      // Check if user has permission to view this order
      if (userId && order.userId !== userId) {
        return errorResponse("Unauthorized to view this order");
      }

      // Calculate order progress
      const progress = this.calculateOrderProgress(order.status);
      
      // Get status timeline
      const timeline = this.buildStatusTimeline(order.statusHistory, order.status);

      return successResponse("Order tracking retrieved successfully", {
        order: {
          ...order,
          progress,
          timeline,
          estimatedDelivery: this.calculateEstimatedDelivery(order.createdAt, order.status)
        }
      });

    } catch (error) {
      console.error('Get order tracking error:', error);
      return errorResponse("Failed to get order tracking");
    }
  }

  /**
   * Get user's orders with tracking
   */
  async getUserOrders(userId: string, page: number = 1, limit: number = 10) {
    try {
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        prisma.order.findMany({
          where: { userId },
          include: {
            items: {
              include: {
                product: {
                  include: {
                    images: {
                      take: 1,
                      orderBy: { sortOrder: 'asc' }
                    }
                  }
                }
              }
            },
            statusHistory: {
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.order.count({
          where: { userId }
        })
      ]);

      const ordersWithProgress = orders.map(order => ({
        ...order,
        progress: this.calculateOrderProgress(order.status),
        latestStatus: order.statusHistory[0] || null
      }));

      return successResponse("User orders retrieved successfully", {
        orders: ordersWithProgress,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('Get user orders error:', error);
      return errorResponse("Failed to get user orders");
    }
  }

  /**
   * Calculate order progress percentage
   */
  private calculateOrderProgress(status: string): number {
    const statusProgress: { [key: string]: number } = {
      'pending': 5,
      'pending_payment': 10,
      'paid': 30,
      'processing': 35, // Add processing status
      'seller_confirmed': 50,
      'shipped': 75,
      'delivered': 100,
      'cancelled': 0
    };

    return statusProgress[status] || 0;
  }

  /**
   * Build status timeline
   */
  private buildStatusTimeline(statusHistory: any[], currentStatus: string) {
    const allStatuses = [
      { key: 'pending', label: 'Order Placed', icon: 'clock', description: 'Your order has been placed and is being processed' },
      { key: 'pending_payment', label: 'Waiting for Payment', icon: 'payment', description: 'Please complete your payment to proceed' },
      { key: 'paid', label: 'Payment Confirmed', icon: 'check', description: 'Your payment has been confirmed successfully' },
      { key: 'processing', label: 'Processing Order', icon: 'cog', description: 'Your order is being prepared' },
      { key: 'seller_confirmed', label: 'Seller Confirmed', icon: 'user-check', description: 'Seller has confirmed and is preparing your order' },
      { key: 'shipped', label: 'Shipped', icon: 'truck', description: 'Your order is on its way to you' },
      { key: 'delivered', label: 'Delivered', icon: 'package-check', description: 'Your order has been delivered successfully' }
    ];

    // Define status order for completion logic
    const statusOrder = ['pending', 'pending_payment', 'paid', 'processing', 'seller_confirmed', 'shipped', 'delivered'];
    const currentStatusIndex = statusOrder.indexOf(currentStatus);

    return allStatuses.map((statusItem, index) => {
      const historyItem = statusHistory.find(h => h.status === statusItem.key);
      const statusIndex = statusOrder.indexOf(statusItem.key);

      // A step is completed if:
      // 1. It has a history entry, OR
      // 2. The current status is further along in the process
      const isCompleted = historyItem !== undefined || statusIndex < currentStatusIndex;
      const isCurrent = currentStatus === statusItem.key;

      return {
        ...statusItem,
        isCompleted,
        isCurrent,
        timestamp: historyItem?.createdAt || null,
        description: historyItem?.description || statusItem.description
      };
    });
  }

  /**
   * Calculate estimated delivery date
   */
  private calculateEstimatedDelivery(orderDate: Date, status: string): Date | null {
    if (status === 'delivered') return null;

    const estimatedDays = status === 'shipped' ? 3 : 7; // 3 days if shipped, 7 days otherwise
    const deliveryDate = new Date(orderDate);
    deliveryDate.setDate(deliveryDate.getDate() + estimatedDays);

    return deliveryDate;
  }

  /**
   * Get default status description
   */
  private getDefaultStatusDescription(status: string): string {
    const descriptions: { [key: string]: string } = {
      'pending_payment': 'Order created, waiting for payment',
      'paid': 'Payment confirmed successfully',
      'seller_confirmed': 'Seller confirmed and preparing shipment',
      'shipped': 'Order has been shipped',
      'delivered': 'Order delivered successfully',
      'cancelled': 'Order has been cancelled'
    };

    return descriptions[status] || 'Status updated';
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId: string, userId: string, reason?: string) {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId }
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      if (order.userId !== userId) {
        return errorResponse("Unauthorized to cancel this order");
      }

      if (['shipped', 'delivered'].includes(order.status)) {
        return errorResponse("Cannot cancel order that has been shipped or delivered");
      }

      const result = await this.updateOrderStatus({
        orderId,
        status: 'cancelled',
        description: reason || 'Order cancelled by customer',
        createdBy: userId
      });

      return result;

    } catch (error) {
      console.error('Cancel order error:', error);
      return errorResponse("Failed to cancel order");
    }
  }
}

export default new OrderTrackingService();
